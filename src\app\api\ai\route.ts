import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import {
  AIMessage,
  StructuredAIResponse,
  ToolCall,
  ToolResult,
  ConversationState,
  AI_RESPONSE_SCHEMA
} from '@/types/ai-messages';
import { AI_CONFIG } from '@/config/ai-limits';

// Types for batch operations
interface BatchOperation {
  op: 'create_element' | 'create_connection' | 'update_element' | 'delete_connection' | 'delete_connection_by_endpoints' | 'delete_element';
  [key: string]: any;
}

// Turn state for accumulating operations
interface TurnState {
  turnId: string;
  operations: BatchOperation[];
  toolCallCount: number;
  isComplete: boolean;
}

// Helper function to resolve template variables in strings
function resolveTemplateVariables(value: string, turnState: TurnState): string {
  if (typeof value !== 'string') {
    return value as unknown as string;
  }

  console.log(`[AI Route] Resolving template variables in: ${value}`);
  console.log(`[AI Route] Available operations:`, turnState.operations.map(op => ({ op: op.op, id: op.id })));

  // Handle different template patterns

  // Pattern 1: RESPONSE_FROM_create_elem_1 style references (no braces)
  if (value.startsWith('RESPONSE_FROM_')) {
    const toolCallRef = value.replace('RESPONSE_FROM_', '');
    console.log(`[AI Route] Looking for tool call reference (pattern 1): ${toolCallRef}`);
    return resolveElementReference(toolCallRef, turnState, value);
  }

  // Pattern 2: {{create_1_1699557371}} style references (simple double braces)
  const templatePattern2 = /\{\{([^}]+)\}\}/g;
  value = value.replace(templatePattern2, (match, toolCallRef) => {
    console.log(`[AI Route] Looking for tool call reference (pattern 2): ${toolCallRef}`);
    return resolveElementReference(toolCallRef, turnState, match);
  });

  // Pattern 3: {{RESPONSE_FROM_create_1693847561_1}} style references
  const templatePattern3 = /\{\{RESPONSE_FROM_([^}]+)\}\}/g;
  value = value.replace(templatePattern3, (match, toolCallRef) => {
    console.log(`[AI Route] Looking for tool call reference (pattern 3): ${toolCallRef}`);
    return resolveElementReference(toolCallRef, turnState, match);
  });

  // Pattern 4a: ${result.create1_1709227992.element_id} style references
  const templatePattern4a = /\$\{result\.([^.]+)\.element_id\}/g;
  value = value.replace(templatePattern4a, (match, toolCallRef) => {
    console.log(`[AI Route] Looking for tool call reference (pattern 4a): ${toolCallRef}`);
    return resolveElementReference(toolCallRef, turnState, match);
  });

  // Pattern 4b: ${response.create_mars_1.element_id} style references (the one causing errors)
  const templatePattern4b = /\$\{response\.([^.]+)\.element_id\}/g;
  value = value.replace(templatePattern4b, (match, toolCallRef) => {
    console.log(`[AI Route] Looking for tool call reference (pattern 4b): ${toolCallRef}`);
    return resolveElementReference(toolCallRef, turnState, match);
  });

  // Pattern 5: Direct element reference like "create_1689534971" (timestamp only)
  if (value.startsWith('create_') && !value.includes('${') && !value.includes('{{') && !value.startsWith('RESPONSE_FROM_')) {
    console.log(`[AI Route] Looking for direct element reference (pattern 5): ${value}`);
    return resolveElementReference(value, turnState, value);
  }

  return value;
}

// Helper function to resolve element references
function resolveElementReference(toolCallRef: string, turnState: TurnState, originalMatch: string): string {
  console.log(`[AI Route] Resolving element reference: ${toolCallRef}`);

  // Try different matching strategies
  let createOperation = null;

  // Strategy 1: Direct ID match
  createOperation = turnState.operations.find(op =>
    op.op === 'create_element' && op.id === toolCallRef
  );

  // Strategy 2: Find by index from create_N_timestamp pattern (create_1_1699557371)
  if (!createOperation && toolCallRef.match(/^create_\d+_\d+$/)) {
    // Handle patterns like "create_1_1699557371" where the first number is the index
    const indexMatch = toolCallRef.match(/^create_(\d+)_\d+$/);
    if (indexMatch) {
      const index = parseInt(indexMatch[1]) - 1; // Convert to 0-based index
      const createOperations = turnState.operations.filter(op => op.op === 'create_element');
      console.log(`[AI Route] Looking for element at create_N_timestamp index ${index} out of ${createOperations.length} elements`);
      if (index >= 0 && index < createOperations.length) {
        createOperation = createOperations[index];
        console.log(`[AI Route] Found element by create_N_timestamp index ${index}: ${createOperation.id}`);
      }
    }
  }

  // Strategy 3: Find by index from create_elem_1 pattern
  if (!createOperation && toolCallRef.includes('create_elem_')) {
    // Handle patterns like "create_elem_1" where the last number is the index
    const indexMatch = toolCallRef.match(/create_elem_(\d+)$/);
    if (indexMatch) {
      const index = parseInt(indexMatch[1]) - 1; // Convert to 0-based index
      const createOperations = turnState.operations.filter(op => op.op === 'create_element');
      console.log(`[AI Route] Looking for element at create_elem index ${index} out of ${createOperations.length} elements`);
      if (index >= 0 && index < createOperations.length) {
        createOperation = createOperations[index];
        console.log(`[AI Route] Found element by create_elem index ${index}: ${createOperation.id}`);
      }
    }
  }

  // Strategy 4: Find by timestamp-only pattern (create_1689534971 = first element)
  if (!createOperation && toolCallRef.match(/^create_\d+$/)) {
    // Handle patterns like "create_1689534971" (timestamp only, assume first element)
    console.log(`[AI Route] Timestamp-only pattern detected: ${toolCallRef}, assuming first element`);
    const createOperations = turnState.operations.filter(op => op.op === 'create_element');
    if (createOperations.length > 0) {
      createOperation = createOperations[0]; // First element
      console.log(`[AI Route] Found element by timestamp-only pattern (first element): ${createOperation.id}`);
    }
  }

  // Strategy 5: Find by index from create_timestamp_index pattern
  if (!createOperation && toolCallRef.includes('create_')) {
    // Handle patterns like "create_1693847561_1" where the last number is the index
    const indexMatch = toolCallRef.match(/create_\d+_(\d+)$/);
    if (indexMatch) {
      const index = parseInt(indexMatch[1]) - 1; // Convert to 0-based index
      const createOperations = turnState.operations.filter(op => op.op === 'create_element');
      console.log(`[AI Route] Looking for element at timestamp index ${index} out of ${createOperations.length} elements`);
      if (index >= 0 && index < createOperations.length) {
        createOperation = createOperations[index];
        console.log(`[AI Route] Found element by timestamp index ${index}: ${createOperation.id}`);
      }
    }
  }

  // Strategy 6: Find by simple index (create1, create2, etc.)
  if (!createOperation && toolCallRef.includes('create')) {
    const indexMatch = toolCallRef.match(/create[_]?(\d+)/);
    if (indexMatch) {
      const index = parseInt(indexMatch[1]) - 1; // Convert to 0-based index
      const createOperations = turnState.operations.filter(op => op.op === 'create_element');
      console.log(`[AI Route] Looking for element at simple index ${index} out of ${createOperations.length} elements`);
      if (index >= 0 && index < createOperations.length) {
        createOperation = createOperations[index];
        console.log(`[AI Route] Found element by simple index ${index}: ${createOperation.id}`);
      }
    }
  }

  // Strategy 7: Handle generic placeholders like "ELEMENT_ID_1", "ELEMENT_ID_2", etc.
  if (!createOperation && toolCallRef.match(/^ELEMENT_ID_(\d+)$/)) {
    const indexMatch = toolCallRef.match(/^ELEMENT_ID_(\d+)$/);
    if (indexMatch) {
      const index = parseInt(indexMatch[1]) - 1; // Convert to 0-based index
      const createOperations = turnState.operations.filter(op => op.op === 'create_element');
      console.log(`[AI Route] Looking for element at generic placeholder index ${index} out of ${createOperations.length} elements`);
      if (index >= 0 && index < createOperations.length) {
        createOperation = createOperations[index];
        console.log(`[AI Route] Found element by generic placeholder index ${index}: ${createOperation.id}`);
      } else {
        console.warn(`[AI Route] Generic placeholder ${toolCallRef} index ${index} out of range. Available elements: ${createOperations.length}`);
      }
    }
  }

  // Strategy 8: Partial match on ID
  if (!createOperation) {
    createOperation = turnState.operations.find(op =>
      op.op === 'create_element' &&
      (toolCallRef.includes(op.id) || op.id.includes(toolCallRef))
    );
    if (createOperation) {
      console.log(`[AI Route] Found element by partial match: ${createOperation.id}`);
    }
  }

  if (createOperation) {
    console.log(`[AI Route] ✅ Resolved template ${originalMatch} to element ID: ${createOperation.id}`);
    return createOperation.id;
  }

  console.warn(`[AI Route] ❌ Could not resolve template variable: ${originalMatch}`);
  console.warn(`[AI Route] Tool call reference was: ${toolCallRef}`);
  console.warn(`[AI Route] Available element IDs:`, turnState.operations.filter(op => op.op === 'create_element').map(op => op.id));
  console.warn(`[AI Route] All operations:`, turnState.operations.map(op => ({ op: op.op, id: op.id })));

  // Return original match instead of a placeholder to help debug
  console.warn(`[AI Route] Returning original match: ${originalMatch}`);
  return originalMatch; // Return original if not found
}

// Helper function to convert tool calls directly to batch operations (without executing)
function convertToolCallToBatchOperation(toolCall: ToolCall, turnState: TurnState): BatchOperation | null {
  const args = toolCall.arguments;

  switch (toolCall.name) {
    case 'create_element':
      // Apply type correction here as well
      let elementType = args?.type || 'sticky-yellow';
      const typeMapping: Record<string, string> = {
        'sticky_note': 'sticky-yellow',
        'sticky-note': 'sticky-yellow',
        'sticky': 'sticky-yellow',
        'note': 'sticky-yellow',
        'sticky_yellow': 'sticky-yellow',
        'sticky_red': 'sticky-red',
        'sticky_blue': 'sticky-blue'
      };

      if (typeMapping[elementType.toLowerCase()]) {
        elementType = typeMapping[elementType.toLowerCase()];
        console.log(`[AI Route] Corrected element type in operation from '${args?.type}' to '${elementType}'`);
      }

      return {
        op: 'create_element',
        id: args?.id || uuidv4(), // Use provided ID or generate one
        type: elementType,
        position: args?.position || { x: 0, y: 0 },
        props: {
          content: args?.content || '',
          title: args?.title || '',
          url: args?.url || '',
          file_url: args?.file_url || '',
          website_url: args?.website_url || '',
          imageUrl: args?.imageUrl || '',
          alt: args?.alt || '',
        },
      };

    case 'create_connection':
      // Log the raw arguments for debugging
      console.log(`[AI Route] Raw connection arguments:`, {
        from_element_id: args?.from_element_id,
        fromId: args?.fromId,
        to_element_id: args?.to_element_id,
        toId: args?.toId,
        allArgs: args
      });

      // Resolve template variables in connection endpoints
      const rawFromId = args?.from_element_id || args?.fromId || '';
      const rawToId = args?.to_element_id || args?.toId || '';

      console.log(`[AI Route] Before resolution - fromId: "${rawFromId}", toId: "${rawToId}"`);

      const fromId = resolveTemplateVariables(rawFromId, turnState);
      const toId = resolveTemplateVariables(rawToId, turnState);

      console.log(`[AI Route] After resolution - fromId: "${fromId}", toId: "${toId}"`);
      console.log(`[AI Route] Creating connection from ${fromId} to ${toId}`);

      // Basic sanity guard: if placeholders remain, keep raw strings but add warning-like label
      const unresolvedPlaceholder =
        typeof fromId === 'string' && /\$\{.*\}|\{\{.*\}\}/.test(fromId) ||
        typeof toId === 'string' && /\$\{.*\}|\{\{.*\}\}/.test(toId);

      if (unresolvedPlaceholder) {
        console.warn(`[AI Route] Unresolved template detected in connection args: from="${fromId}", to="${toId}"`);
      }

      return {
        op: 'create_connection',
        id: args?.id || uuidv4(),
        from_id: fromId,
        to_id: toId,
        label: args?.label || '',
      };

    case 'update_element':
      return {
        op: 'update_element',
        element_id: resolveTemplateVariables(args?.element_id || args?.elementId || '', turnState),
        updates: args?.updates || {},
      };

    case 'delete_element':
      return {
        op: 'delete_element',
        element_id: resolveTemplateVariables(args?.element_id || args?.elementId || '', turnState),
      };

    case 'delete_connection':
      if (args?.connection_id || args?.connectionId) {
        return {
          op: 'delete_connection',
          connection_id: resolveTemplateVariables(args?.connection_id || args?.connectionId || '', turnState),
        };
      } else if (args?.from_element_id && args?.to_element_id) {
        return {
          op: 'delete_connection_by_endpoints',
          from_id: resolveTemplateVariables(args?.from_element_id || '', turnState),
          to_id: resolveTemplateVariables(args?.to_element_id || '', turnState),
        };
      }
      break;

    default:
      return null;
  }

  return null;
}



// Helper function to update board cache for immediate read_board calls
function updateBoardCacheForOperation(
  operation: BatchOperation,
  boardData: any,
  boardStateCache: BoardStateCache
): void {
  switch (operation.op) {
    case 'create_element':
      const newElement = {
        id: operation.id,
        type: operation.type,
        content: operation.props.content || '',
        title: operation.props.title || '',
        url: operation.props.url || '',
        position: operation.position,
        position_x: operation.position.x,
        position_y: operation.position.y,
      };

      // Update board data
      if (boardData) {
        boardData.elements = boardData.elements || [];
        boardData.elements.push(newElement);
      }

      // Update cache
      if (boardStateCache) {
        boardStateCache.elements.set(operation.id, newElement);
        const contentHash = `${operation.type}:${operation.props.content}:${operation.position.x}:${operation.position.y}`;
        boardStateCache.elementsByContent.set(contentHash, operation.id);
      }

      console.log(`[AI Route] Updated cache with element ${operation.id} for immediate read_board access`);
      break;

    case 'create_connection':
      const newConnection = {
        id: operation.id,
        fromId: operation.from_id,
        toId: operation.to_id,
        label: operation.label || '',
      };

      // Update board data
      if (boardData) {
        boardData.connections = boardData.connections || [];
        boardData.connections.push(newConnection);
      }

      // Update cache
      if (boardStateCache) {
        boardStateCache.connections.set(operation.id, newConnection);
      }

      console.log(`[AI Route] Updated cache with connection ${operation.id} for immediate read_board access`);
      break;

    // Add other operation types as needed
    default:
      console.log(`[AI Route] Cache update not implemented for operation: ${operation.op}`);
  }
}

// Import authentication utilities
import { getAuthenticatedUser } from '@/utils/authUtils';
import { createClient } from '@supabase/supabase-js';

// Helper function to process batch operations directly (avoiding internal API call)
async function processBatchOperationsDirectly(
  turnId: string,
  boardId: string,
  operations: BatchOperation[],
  request: NextRequest
): Promise<any> {
  try {
    console.log(`[AI Route] Processing batch operations directly for turn ${turnId} with ${operations.length} operations`);

    // Get authenticated user using the same method as AI tools
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      console.error('[AI Route] Authentication failed for batch operations:', authError);
      throw new Error('Authentication required for batch operations');
    }

    // Create server-side Supabase client with service role key for database operations
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

    const supabaseServer = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    console.log(`[AI Route] Authenticated user ${user.id} for batch operations`);

    // Process operations using the same logic as batch-persist endpoint
    const results = {
      created_elements: [] as any[],
      created_connections: [] as any[],
      updated_elements: [] as any[],
      deleted_element_ids: [] as string[],
      deleted_connection_ids: [] as string[],
      warnings: [] as Array<{ code: string; message: string }>,
    };

    // Sort operations by phase (same as batch-persist endpoint)
    const sortedOperations = operations.sort((a, b) => {
      const getPhase = (op: string) => {
        switch (op) {
          case 'create_element': return 1;
          case 'create_connection': return 2;
          case 'update_element': return 3;
          case 'delete_connection':
          case 'delete_connection_by_endpoints': return 4;
          case 'delete_element': return 5;
          default: return 999;
        }
      };
      return getPhase(a.op) - getPhase(b.op);
    });

    console.log(`[AI Route] Processing operations in order:`, sortedOperations.map(op => op.op));

    // Process each operation using existing manage_element and manage_connection RPCs
    for (const operation of sortedOperations) {
      try {
        switch (operation.op) {
          case 'create_element':
            // Validate and correct the element type (same logic as AI tools)
            const validTypes = ['sticky-yellow', 'sticky-red', 'sticky-blue', 'text', 'article', 'image'];
            let correctedType = operation.type.toLowerCase();

            // Map common incorrect types to valid ones
            const typeMapping: Record<string, string> = {
              'sticky_note': 'sticky-yellow',
              'sticky-note': 'sticky-yellow',
              'sticky': 'sticky-yellow',
              'note': 'sticky-yellow',
              'sticky_yellow': 'sticky-yellow',
              'sticky_red': 'sticky-red',
              'sticky_blue': 'sticky-blue'
            };

            if (typeMapping[correctedType]) {
              correctedType = typeMapping[correctedType];
              console.log(`[AI Route] Corrected element type from '${operation.type}' to '${correctedType}'`);
            } else if (!validTypes.includes(correctedType)) {
              console.warn(`[AI Route] Invalid element type '${operation.type}', defaulting to 'sticky-yellow'`);
              correctedType = 'sticky-yellow';
            }

            const elementData = {
              id: operation.id,
              type: correctedType, // Use corrected type
              position: operation.position,
              content: operation.props.content || '',
              title: operation.props.title || correctedType,
              url: operation.props.url || operation.props.file_url || '',
              file_url: operation.props.file_url || operation.props.url || '',
              website_url: operation.props.website_url || (correctedType === 'article' ? operation.props.url : ''),
              width: operation.props.width || null,
              height: operation.props.height || null,
              isAiGenerated: true,
            };

            const { data: elementResult, error: elementError } = await supabaseServer.rpc('manage_element', {
              p_user_id: user.id,
              p_board_id: boardId,
              p_action: 'add',
              p_element: elementData,
            });

            if (elementError) {
              throw new Error(`Failed to create element ${operation.id}: ${elementError.message}`);
            }

            // Format element result for frontend consumption
            const formattedElement = {
              id: operation.id,
              type: correctedType, // Use corrected type
              content: operation.props.content || '',
              title: operation.props.title || '',
              url: operation.props.url || operation.props.file_url || '',
              file_url: operation.props.file_url || '',
              position: {
                x: operation.position.x,
                y: operation.position.y
              },
              isAiGenerated: true
            };

            results.created_elements.push(formattedElement);
            console.log(`[AI Route] Successfully created element ${operation.id}:`, formattedElement);
            break;

          case 'create_connection':
            console.log(`[AI Route] Creating connection with operation:`, {
              id: operation.id,
              from_id: operation.from_id,
              to_id: operation.to_id,
              label: operation.label
            });

            // Defensive UUID validation before DB call
            const UUID_RE = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            const isUuid = (v: any) => typeof v === 'string' && UUID_RE.test(v);

            if (!isUuid(operation.from_id)) {
              throw new Error(`Invalid UUID for from_id in create_connection ${operation.id}: "${operation.from_id}"`);
            }
            if (!isUuid(operation.to_id)) {
              throw new Error(`Invalid UUID for to_id in create_connection ${operation.id}: "${operation.to_id}"`);
            }

            const connectionData = {
              id: operation.id,
              fromId: operation.from_id,
              toId: operation.to_id,
              label: operation.label || '',
              type: 'default',
              isAiGenerated: true,
            };

            const { data: connectionResult, error: connectionError } = await supabaseServer.rpc('manage_connection', {
              p_user_id: user.id,
              p_board_id: boardId,
              p_action: 'add',
              p_connection: connectionData,
            });

            if (connectionError) {
              throw new Error(`Failed to create connection ${operation.id}: ${connectionError.message}`);
            }

            // Format connection result for frontend consumption
            const formattedConnection = {
              id: operation.id,
              fromId: operation.from_id,
              toId: operation.to_id,
              label: operation.label || '',
              type: 'default',
              isAiGenerated: true
            };

            results.created_connections.push(formattedConnection);
            console.log(`[AI Route] Successfully created connection ${operation.id}:`, formattedConnection);
            break;

          case 'update_element':
            const updateData = {
              id: operation.element_id,
              ...operation.updates,
            };

            const { error: updateError } = await supabaseServer.rpc('manage_element', {
              p_user_id: user.id,
              p_board_id: boardId,
              p_action: 'update',
              p_element: updateData,
            });

            if (updateError) {
              throw new Error(`Failed to update element ${operation.element_id}: ${updateError.message}`);
            }

            results.updated_elements.push({ id: operation.element_id, updates: operation.updates });
            console.log(`[AI Route] Successfully updated element ${operation.element_id}`);
            break;

          case 'delete_element':
            const deleteElementData = {
              id: operation.element_id,
            };

            const { error: deleteElementError } = await supabaseServer.rpc('manage_element', {
              p_user_id: user.id,
              p_board_id: boardId,
              p_action: 'delete',
              p_element: deleteElementData,
            });

            if (deleteElementError) {
              throw new Error(`Failed to delete element ${operation.element_id}: ${deleteElementError.message}`);
            }

            results.deleted_element_ids.push(operation.element_id);
            console.log(`[AI Route] Successfully deleted element ${operation.element_id}`);
            break;

          case 'delete_connection':
            const deleteConnectionData = {
              id: operation.connection_id,
            };

            const { error: deleteConnectionError } = await supabaseServer.rpc('manage_connection', {
              p_user_id: user.id,
              p_board_id: boardId,
              p_action: 'delete',
              p_connection: deleteConnectionData,
            });

            if (deleteConnectionError) {
              throw new Error(`Failed to delete connection ${operation.connection_id}: ${deleteConnectionError.message}`);
            }

            results.deleted_connection_ids.push(operation.connection_id);
            console.log(`[AI Route] Successfully deleted connection ${operation.connection_id}`);
            break;

          default:
            console.warn(`[AI Route] Unsupported operation type: ${operation.op}`);
        }
      } catch (error: any) {
        console.error(`[AI Route] Error processing operation ${operation.op}:`, error);
        throw error; // Re-throw to trigger transaction rollback
      }
    }

    const result = {
      turn_id: turnId,
      board_id: boardId,
      results,
    };

    console.log(`[AI Route] Batch operations completed successfully for turn ${turnId}`);
    console.log(`[AI Route] Final results summary:`, {
      created_elements: results.created_elements.length,
      created_connections: results.created_connections.length,
      updated_elements: results.updated_elements.length,
      deleted_element_ids: results.deleted_element_ids.length,
      deleted_connection_ids: results.deleted_connection_ids.length,
      warnings: results.warnings.length
    });
    console.log(`[AI Route] Created elements:`, results.created_elements);
    console.log(`[AI Route] Created connections:`, results.created_connections);

    return result;

  } catch (error) {
    console.error(`[AI Route] Batch operations error for turn ${turnId}:`, error);
    throw error;
  }
}

// Updated system prompt for structured JSON responses with enhanced connection guidance
const SYSTEM_PROMPT = `You are an AI Research Assistant for the Detective Board application. You help users research topics and manage their detective-style boards.

You must ALWAYS respond in structured JSON format. You can either:
1. Provide content to the user (with control over whether conversation continues)
2. Call a tool to perform an action

Your response must be valid JSON matching this schema:
{
  "type": "content" | "tool_call",
  "content": "string (required if type is content)",
  "finished": "boolean (optional, for content only - defaults to true)",
  "tool_call": {
    "id": "unique_id",
    "name": "tool_name",
    "arguments": {...}
  } (required if type is tool_call)
}

COMMUNICATION FLEXIBILITY: You have full freedom to communicate throughout your work:
- You can provide updates, explanations, or context at any point
- Use "finished": false in content responses to continue the conversation after providing text
- Use "finished": true (or omit) in content responses when you're completely done
- You can mix content and tool calls freely: content -> tool -> content -> tool -> content (finished)

Available tools:
- read_board: View current board contents (no parameters) - Returns available element IDs for connections
- create_element: Add sticky notes, text, articles, or images (type, position, content). For images, provide the image URL in 'url' parameter and caption in 'content' - the system will automatically download and store the image.
- create_connection: Connect elements (from_element_id, to_element_id) - Use exact element IDs from read_board or newly created elements - can create connections between any elements besides those of type text
- move_viewport: Adjust user's view (target_position, zoom_level)
- update_element: Modify elements (element_id, updates)
- delete_element: Remove elements (element_id)
- delete_connection: Remove connections (connection_id OR from_element_id + to_element_id)
- exa_search: Search web for multiple sources, do not include a specific timeframe in your query unless explicitly requested by the user (search_term, num_results, search_options)
- exa_answer: Get direct answers to questions, do not include a specific timeframe in your query unless explicitly requested by the user (question)
- exa_research: Conduct comprehensive research, do not include a specific timeframe in your query unless explicitly requested by the user (research_topic)

Rules:
1. Be proactive - automatically add research findings to the board
2. Use tools to gather information before responding
3. Provide helpful, engaging responses
4. Always use proper JSON format
5. Generate unique IDs for tool calls using timestamp + random
6. Do not include specific years in search queries unless explicitly requested
7. Don't bother mentioning the id of elements or connections. Just use the name or a summary of the content.
8. Communicate freely throughout your work - provide updates, explanations, and context as needed
9. When creating connections, use the exact element IDs from previous tool results or board reads
10. Never try to disprove what the user is attempting to prove or discover using their board. Only add information that supports their hypothesis.
11. Board Dimensions are 19000x9200 with the middle being positon 9500, 4600. Create elements around the middle of the board if board is empty otherwise place elements in relation to other elements.
12. When attempting to add elements to display evidence never add only one element to establish a point. Always add multiple elements placed strategicaly and with relevant connections to create an aesthetic board.

Examples:
Continuing content: {"type": "content", "content": "I'll help you research that topic. Let me start by checking your current board.", "finished": false}
Tool call: {"type": "tool_call", "tool_call": {"id": "read_board_123", "name": "read_board", "arguments": {}}}
Final content: {"type": "content", "content": "I've completed all the requested actions successfully!", "finished": true}
Ending content (default): {"type": "content", "content": "Here's your research summary with new board elements added."}`;

// Enhanced board state cache for efficient ID tracking
interface BoardStateCache {
  elements: Map<string, any>; // Map of element ID to element data
  connections: Map<string, any>; // Map of connection ID to connection data
  elementsByContent: Map<string, string>; // Map of content hash to element ID for deduplication
}

// Helper function to construct proper base URL for server-side requests
function getBaseUrl(): string {
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  return 'http://localhost:3000';
}

// Tool calling helper function with enhanced ID tracking
async function callTool(
  toolCall: ToolCall,
  request: NextRequest,
  boardId?: string,
  boardData?: any,
  boardStateCache?: BoardStateCache
): Promise<ToolResult> {
  try {
    // Only add board_id to parameters for tools that actually need it
    const boardRelatedTools = ['read_board', 'create_element', 'create_connection', 'move_viewport', 'update_element', 'delete_element', 'delete_connection'];
    const updatedParameters = { ...toolCall.arguments };
    if (boardId && boardRelatedTools.includes(toolCall.name)) {
      updatedParameters.board_id = boardId;
    }

    const requestBody: any = {
      tool_name: toolCall.name,
      parameters: updatedParameters,
      board_id: boardId
    };

    // For read_board, we need to get fresh data, not the stale conversation snapshot
    if (toolCall.name === 'read_board') {
      console.log(`[AI Route callTool] read_board called - will fetch fresh data from database instead of using stale snapshot`);
      // Don't pass board_data for read_board - let it fetch fresh data from database
      // This ensures we see manually added elements, not just the conversation snapshot
    } else {
      // For other tools, pass the board data if available
      if (boardData) {
        requestBody.board_data = boardData;
      }
    }

    // Enhanced connection validation using cached IDs
    if (toolCall.name === 'create_connection' && boardStateCache) {
      const fromId = updatedParameters.from_element_id;
      const toId = updatedParameters.to_element_id;

      console.log(`[AI Route] Validating connection: ${fromId} -> ${toId}`);
      console.log(`[AI Route] Available element IDs in cache: ${Array.from(boardStateCache.elements.keys()).join(', ')}`);

      // Check if both elements exist in our cache
      if (!boardStateCache.elements.has(fromId)) {
        console.error(`[AI Route] Source element ${fromId} not found in cache`);
        return {
          success: false,
          error: `Source element with ID ${fromId} not found. Please ensure the element exists before creating a connection.`
        };
      }

      if (!boardStateCache.elements.has(toId)) {
        console.error(`[AI Route] Target element ${toId} not found in cache`);
        return {
          success: false,
          error: `Target element with ID ${toId} not found. Please ensure the element exists before creating a connection.`
        };
      }

      console.log(`[AI Route] Connection validation passed for ${fromId} -> ${toId}`);
    }

    const response = await fetch(`${getBaseUrl()}/api/ai/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('cookie') || '',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Tool ${toolCall.name} failed`);
    }

    const result = await response.json();

    // Enhanced board data and cache updates
    if (toolCall.name === 'create_element' && result.success && result.data?.element) {
      const newElement = result.data.element;
      console.log(`[AI Route] Updating in-memory board data with new element: ${newElement.id} (${newElement.type})`);

      // Update board data
      if (boardData) {
        boardData.elements = boardData.elements || [];
        boardData.elements.push({
          id: newElement.id,
          type: newElement.type,
          content: newElement.content,
          title: newElement.title,
          url: newElement.url,
          position: newElement.position
        });
        console.log(`[AI Route] Board data now has ${boardData.elements.length} elements`);
      }

      // Update cache for immediate ID tracking
      if (boardStateCache) {
        boardStateCache.elements.set(newElement.id, newElement);

        // Create content hash for deduplication
        const contentHash = `${newElement.type}:${newElement.content}:${newElement.position.x}:${newElement.position.y}`;
        boardStateCache.elementsByContent.set(contentHash, newElement.id);

        console.log(`[AI Route] Added element ${newElement.id} to cache. Cache now has ${boardStateCache.elements.size} elements`);
      }
    } else if (toolCall.name === 'create_connection' && result.success && result.data?.connection) {
      const newConnection = result.data.connection;
      console.log(`[AI Route] Updating in-memory board data with new connection: ${newConnection.fromId} -> ${newConnection.toId}`);

      // Update board data
      if (boardData) {
        boardData.connections = boardData.connections || [];
        boardData.connections.push({
          id: newConnection.id,
          fromId: newConnection.fromId,
          toId: newConnection.toId,
          label: newConnection.label
        });
        console.log(`[AI Route] Board data now has ${boardData.connections.length} connections`);
      }

      // Update cache
      if (boardStateCache) {
        boardStateCache.connections.set(newConnection.id, newConnection);
        console.log(`[AI Route] Added connection ${newConnection.id} to cache. Cache now has ${boardStateCache.connections.size} connections`);
      }
    } else if (toolCall.name === 'update_element' && result.success && result.data?.operation) {
      const operation = result.data.operation;
      console.log(`[AI Route] Processing update_element operation:`, operation);

      // Update board data
      if (boardData && operation.elementId) {
        const elementIndex = boardData.elements.findIndex((el: any) => el.id === operation.elementId);
        if (elementIndex !== -1) {
          boardData.elements[elementIndex] = { ...boardData.elements[elementIndex], ...operation.updates };
          console.log(`[AI Route] Updated element ${operation.elementId} in board data`);
        }
      }

      // Update cache
      if (boardStateCache && operation.elementId) {
        const cachedElement = boardStateCache.elements.get(operation.elementId);
        if (cachedElement) {
          const updatedElement = { ...cachedElement, ...operation.updates };
          boardStateCache.elements.set(operation.elementId, updatedElement);
          console.log(`[AI Route] Updated element ${operation.elementId} in cache`);
        }
      }
    } else if (toolCall.name === 'delete_element' && result.success && result.data?.operation) {
      const operation = result.data.operation;
      console.log(`[AI Route] Processing delete_element operation:`, operation);

      // Update board data
      if (boardData && operation.elementId) {
        boardData.elements = boardData.elements.filter((el: any) => el.id !== operation.elementId);
        // Also remove any connections to/from this element
        boardData.connections = boardData.connections.filter((conn: any) =>
          conn.fromId !== operation.elementId && conn.toId !== operation.elementId
        );
        console.log(`[AI Route] Removed element ${operation.elementId} from board data`);
      }

      // Update cache
      if (boardStateCache && operation.elementId) {
        boardStateCache.elements.delete(operation.elementId);
        // Remove connections involving this element
        for (const [connId, conn] of boardStateCache.connections.entries()) {
          if (conn.fromId === operation.elementId || conn.toId === operation.elementId) {
            boardStateCache.connections.delete(connId);
          }
        }
        console.log(`[AI Route] Removed element ${operation.elementId} from cache`);
      }
    } else if (toolCall.name === 'delete_connection' && result.success && result.data?.operation) {
      const operation = result.data.operation;
      console.log(`[AI Route] Processing delete_connection operation:`, operation);

      // Update board data
      if (boardData) {
        if (operation.connectionId) {
          // Delete by connection ID
          boardData.connections = boardData.connections.filter((conn: any) => conn.id !== operation.connectionId);
          console.log(`[AI Route] Removed connection ${operation.connectionId} from board data`);
        } else if (operation.fromElementId && operation.toElementId) {
          // Delete by element IDs
          boardData.connections = boardData.connections.filter((conn: any) =>
            !(conn.fromId === operation.fromElementId && conn.toId === operation.toElementId)
          );
          console.log(`[AI Route] Removed connection ${operation.fromElementId} -> ${operation.toElementId} from board data`);
        }
      }

      // Update cache
      if (boardStateCache) {
        if (operation.connectionId) {
          boardStateCache.connections.delete(operation.connectionId);
          console.log(`[AI Route] Removed connection ${operation.connectionId} from cache`);
        } else if (operation.fromElementId && operation.toElementId) {
          // Find and remove connection by element IDs
          for (const [connId, conn] of boardStateCache.connections.entries()) {
            if (conn.fromId === operation.fromElementId && conn.toId === operation.toElementId) {
              boardStateCache.connections.delete(connId);
              console.log(`[AI Route] Removed connection ${connId} (${operation.fromElementId} -> ${operation.toElementId}) from cache`);
              break;
            }
          }
        }
      }
    }

    return result;
  } catch (error) {
    console.error(`Error calling tool ${toolCall.name}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Tool execution failed'
    };
  }
}

// New structured message processing with enhanced board state caching
async function processStructuredConversation(
  messages: AIMessage[],
  model: string,
  request: NextRequest,
  boardId?: string,
  boardData?: any
): Promise<Response> {
  // Create a mutable reference to board data that persists throughout the conversation
  // CRITICAL FIX: Always use the fresh board data passed to this conversation
  // The client sends fresh board data with each message, so we should use it directly
  const mutableBoardData = boardData ? { ...boardData } : null;
  console.log(`[AI Route] Starting conversation with ${mutableBoardData?.elements?.length || 0} elements in board data`);
  console.log(`[AI Route] Board data elements:`, mutableBoardData?.elements?.map((e: { id: string }) => e.id) || []);

  // Initialize enhanced board state cache for efficient ID tracking
  const boardStateCache: BoardStateCache = {
    elements: new Map(),
    connections: new Map(),
    elementsByContent: new Map()
  };

  // Initialize turn state for batch operations
  const turnState: TurnState = {
    turnId: uuidv4(),
    operations: [],
    toolCallCount: 0,
    isComplete: false
  };

  console.log(`[AI Route] Starting turn ${turnState.turnId} for board ${boardId}`);

  // Helper to check if a tool call should be batched
  const isBatchableToolCall = (toolName: string): boolean => {
    return ['create_element', 'create_connection', 'update_element', 'delete_element', 'delete_connection'].includes(toolName);
  };



  // Populate cache with existing board data
  if (mutableBoardData) {
    // Cache existing elements
    if (mutableBoardData.elements) {
      mutableBoardData.elements.forEach((element: any) => {
        boardStateCache.elements.set(element.id, element);

        // Create content hash for deduplication
        const contentHash = `${element.type}:${element.content}:${element.position.x}:${element.position.y}`;
        boardStateCache.elementsByContent.set(contentHash, element.id);
      });
      console.log(`[AI Route] Cached ${boardStateCache.elements.size} existing elements`);
    }

    // Cache existing connections
    if (mutableBoardData.connections) {
      mutableBoardData.connections.forEach((connection: any) => {
        boardStateCache.connections.set(connection.id, connection);
      });
      console.log(`[AI Route] Cached ${boardStateCache.connections.size} existing connections`);
    }
  }

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      let streamClosed = false;
      
      // Helper to send structured chunks (with safety check)
      const sendChunk = (data: any) => {
        if (streamClosed) {
          console.log('[AI_STREAM] Attempted to send chunk to closed stream:', data);
          return;
        }
        try {
          const chunk = JSON.stringify(data);
          controller.enqueue(encoder.encode(`data: ${chunk}\n\n`));
        } catch (error: any) {
          if (error.code === 'ERR_INVALID_STATE') {
          } else {
            throw error;
          }
        }
      };
      
      // Helper to send tool status updates
      const sendToolUpdate = (toolCall: ToolCall, status: 'executing' | 'completed' | 'failed', result?: any, error?: string) => {
        console.log('[AI /api/ai/route.ts] Sending tool update:', toolCall, status, result, error);
        sendChunk({
          type: 'tool_update',
          tool_call_id: toolCall.id,
          tool_name: toolCall.name,
          status,
          result,
          error
        });
      };

      // Helper to send content updates (for streaming text)
      const sendContentUpdate = (content: string, finished: boolean = false, isIncremental: boolean = true) => {
        sendChunk({
          type: 'content',
          content,
          finished,
          incremental: isIncremental
        });
      };

      // Helper to send thinking status
      const sendThinkingUpdate = (isThinking: boolean) => {
        sendChunk({
          type: 'thinking',
          thinking: isThinking
        });
      };

      try {
        const apiKey = process.env.OPEN_ROUTER_API_KEY;
        if (!apiKey) {
          throw new Error('OpenRouter API key is not configured');
        }

        let conversationMessages = [...messages];
        let hasMoreTools = true;
        let toolCallCount = 0;
        const maxToolCalls = AI_CONFIG.MAX_TOOL_CALLS;
        let completionSent = false;

        // Track streaming state
        // Note: Content accumulation is now handled incrementally in the frontend

        while (hasMoreTools && toolCallCount < maxToolCalls) {
          console.log(`[AI Route] Starting loop iteration: hasMoreTools=${hasMoreTools}, toolCallCount=${toolCallCount}/${maxToolCalls}`);

          // Request structured JSON response with streaming enabled
          const processedMessages = conversationMessages
            .filter(msg => msg.role && ('content' in msg || 'tool_call' in msg))
            .map(msg => {
              // Handle different message content formats
              if ('content' in msg) {
                if (typeof msg.content === 'string') {
                  // Simple string content
                  return { role: msg.role, content: msg.content };
                } else if (Array.isArray(msg.content)) {
                  // Complex content array (tool use/result messages)
                  return { role: msg.role, content: msg.content };
                }
              }
              return { role: msg.role, content: '' };
            });

          console.log(`[AI Route] Sending ${processedMessages.length} messages to AI model (toolCallCount: ${toolCallCount}/${maxToolCalls})`);
          console.log(`[AI Route] Last 3 messages:`, processedMessages.slice(-3).map(m => ({
            role: m.role,
            contentType: typeof m.content,
            contentPreview: typeof m.content === 'string' ? m.content.substring(0, 100) : Array.isArray(m.content) ? `Array[${(m.content as any[]).length}]` : 'unknown'
          })));

          const payload = {
            model: model === 'standard' ? 'anthropic/claude-3.5-sonnet:beta' : model,
            messages: [
              { role: 'system', content: SYSTEM_PROMPT },
              ...processedMessages
            ],
            response_format: {
              type: 'json_schema',
              json_schema: {
                name: 'ai_response',
                schema: AI_RESPONSE_SCHEMA
              }
            },
            stream: true  // Enable streaming
          };

          const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`,
              'HTTP-Referer': process.env.YOUR_SITE_URL || process.env.VERCEL_URL || 'http://localhost:3000',
              'X-Title': process.env.YOUR_SITE_NAME || 'Detective Board'
            },
            body: JSON.stringify(payload)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error(`[AI Route] AI API request failed with status ${response.status}:`, errorText);
            console.error(`[AI Route] Request payload:`, JSON.stringify(payload, null, 2));
            throw new Error(`AI API request failed with status ${response.status}: ${errorText}`);
          }

          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error('No readable stream available');
          }

          const decoder = new TextDecoder();
          let buffer = '';
          let currentlyStreaming = false;
          let currentToolCall: ToolCall | null = null;

          // Robust JSON framing state
          let framingBuffer = '';
          let lastSentLength = 0;
          let isThinking = false;

          // Helper: extract complete top-level JSON objects, string/escape aware
          const extractCompleteJsonObjects = (input: string): { objects: string[]; rest: string } => {
            const objects: string[] = [];
            let restStart = 0;
            let i = 0;
            let inString = false;
            let escape = false;
            let depth = 0;
            let objStart = -1;

            while (i < input.length) {
              const ch = input[i];

              if (inString) {
                if (escape) {
                  escape = false;
                } else if (ch === '\\') {
                  escape = true;
                } else if (ch === '"') {
                  inString = false;
                }
                i++;
                continue;
              }

              if (ch === '"') {
                inString = true;
                i++;
                continue;
              }

              if (ch === '{') {
                if (depth === 0) objStart = i;
                depth++;
              } else if (ch === '}') {
                depth--;
                if (depth === 0 && objStart !== -1) {
                  const objStr = input.slice(objStart, i + 1);
                  objects.push(objStr);
                  restStart = i + 1;
                  objStart = -1;
                }
              }
              i++;
            }

            const rest = input.slice(restStart);
            return { objects, rest };
          };

          // Helper: try stream incremental "content" field from an in-progress JSON buffer
          const streamContentIncrementally = (jsonPrefix: string) => {
            const typeMatch = jsonPrefix.match(/"type"\s*:\s*"content"/);
            if (!typeMatch) return;

            const contentStartMatch = jsonPrefix.match(/"content"\s*:\s*"/);
            if (!contentStartMatch) return;

            const contentStart = contentStartMatch.index! + contentStartMatch[0].length;
            let currentPos = contentStart;
            let escapeNext = false;
            let inString = true; // we are inside the opening quote
            let extractedContent = '';

            while (currentPos < jsonPrefix.length) {
              const char = jsonPrefix[currentPos];

              if (escapeNext) {
                // Handle common escapes
                if (char === 'n') extractedContent += '\n';
                else if (char === 't') extractedContent += '\t';
                else if (char === '\\') extractedContent += '\\';
                else if (char === '"') extractedContent += '"';
                else extractedContent += char;
                escapeNext = false;
              } else if (char === '\\') {
                escapeNext = true;
              } else if (char === '"' && inString) {
                // likely the end of the string; stop without consuming terminator
                inString = false;
                break;
              } else {
                extractedContent += char;
              }
              currentPos++;
            }

            if (extractedContent.length > lastSentLength) {
              const newChunk = extractedContent.slice(lastSentLength);
              if (newChunk.length > 0) {
                sendContentUpdate(newChunk, false, true);
                lastSentLength = extractedContent.length;
              }
            }
          };

          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              buffer += decoder.decode(value, { stream: true });
              const lines = buffer.split('\n');
              buffer = lines.pop() || '';

              for (const line of lines) {
                if (!line.startsWith('data: ')) continue;
                const data = line.slice(6);
                if (data === '[DONE]') {
                  break;
                }

                // Parse OpenRouter SSE envelope safely
                let delta: string | undefined;
                try {
                  const parsed = JSON.parse(data);
                  delta = parsed.choices?.[0]?.delta?.content;
                } catch {
                  // Non-JSON envelope, skip
                  continue;
                }

                if (!delta) continue;

                // Thinking token filtering
                const hasReservedTokens = /<\|reserved_token_\d+\|>/.test(delta) ||
                                         /<\|[^|]*\|>/.test(delta) ||
                                         /\uFFFD/.test(delta);
                if (hasReservedTokens && !currentlyStreaming && !isThinking) {
                  isThinking = true;
                  sendThinkingUpdate(true);
                }

                // Sanitize delta
                const filteredDelta = delta
                  .replace(/<\|reserved_token_\d+\|>/g, '')
                  .replace(/<\|[^|]*\|>/g, '')
                  .replace(/\uFFFD/g, '');

                if (!filteredDelta) continue;

                if (isThinking) {
                  isThinking = false;
                  sendThinkingUpdate(false);
                }

                // Append into framing buffer
                framingBuffer += filteredDelta;

                // Opportunistic incremental content streaming from partial buffer
                streamContentIncrementally(framingBuffer);

                // Try extracting complete JSON objects
                const { objects, rest } = extractCompleteJsonObjects(framingBuffer);
                framingBuffer = rest;

                for (const objStr of objects) {
                  // Reset incremental counters for each complete object
                  lastSentLength = 0;

                  // Detect tool call early to emit "executing" status before parse errors elsewhere
                  try {
                    const toolCallMatch = objStr.match(/"type"\s*:\s*"tool_call".*?"name"\s*:\s*"([^"]*)".*?"id"\s*:\s*"([^"]*)"/s) ||
                                          objStr.match(/"type"\s*:\s*"tool_call".*?"id"\s*:\s*"([^"]*)".*?"name"\s*:\s*"([^"]*)"/s);
                    if (toolCallMatch && !currentToolCall) {
                      const [, nameOrId, idOrName] = toolCallMatch;
                      const nameIndex = objStr.indexOf('"name"');
                      const idIndex = objStr.indexOf('"id"');
                      const toolName = nameIndex !== -1 && nameIndex < idIndex ? nameOrId : idOrName;
                      const toolId = nameIndex !== -1 && nameIndex < idIndex ? idOrName : nameOrId;
                      currentToolCall = { id: toolId, name: toolName, arguments: {} };
                      sendToolUpdate(currentToolCall, 'executing');
                    }
                  } catch {
                    // ignore tool detection errors
                  }

                  // Now safely parse the complete object
                  let structuredResponse: StructuredAIResponse | null = null;
                  try {
                    structuredResponse = JSON.parse(objStr) as StructuredAIResponse;
                  } catch (parseError) {
                    console.log('[AI_STREAM] Failed to parse complete JSON object, skipping:', (parseError as Error).message);
                    continue;
                  }

                  console.log('[AI /api/ai/route.ts] Parsed structuredResponse:', JSON.stringify(structuredResponse, null, 2));

                  // Handle structured response
                  if (structuredResponse.type === 'content') {
                    const finished = structuredResponse.finished !== false;
                    if (finished) {
                      sendContentUpdate('', true, false);
                      hasMoreTools = false;
                      break;
                    } else {
                      if (structuredResponse.content) {
                        conversationMessages.push({
                          role: 'assistant',
                          content: structuredResponse.content
                        });
                      }
                      break;
                    }
                  } else if (structuredResponse.type === 'tool_call' && structuredResponse.tool_call) {
                    const toolCall: ToolCall = structuredResponse.tool_call;
                    toolCallCount++;

                    if (!currentToolCall || (currentToolCall as ToolCall).id !== toolCall.id) {
                      sendToolUpdate(toolCall, 'executing');
                    }

                    // Declare toolResult variable outside the conditional blocks
                    let toolResult: ToolResult | null = null;

                    if (isBatchableToolCall(toolCall.name)) {
                      console.log(`[AI Route] Queuing batchable operation: ${toolCall.name}`);
                      const batchOperation = convertToolCallToBatchOperation(toolCall, turnState);
                      if (batchOperation) {
                        turnState.operations.push(batchOperation);
                        turnState.toolCallCount++;
                        console.log(`[AI Route] Added operation ${batchOperation.op} with ID ${batchOperation.id} to turn ${turnState.turnId} (${turnState.operations.length} total)`);

                        sendToolUpdate(toolCall, 'completed', {
                          queued_for_batch: true,
                          turn_id: turnState.turnId,
                          operation: batchOperation,
                          message: `Operation ${batchOperation.op} queued for batch processing`
                        });

                        updateBoardCacheForOperation(batchOperation, mutableBoardData, boardStateCache);
                      } else {
                        console.warn(`[AI Route] Failed to convert tool call to batch operation for ${toolCall.name}`);
                        sendToolUpdate(toolCall, 'failed', undefined, 'Failed to queue operation for batch processing');
                      }
                    } else {
                      toolResult = await callTool(toolCall, request, boardId, mutableBoardData, boardStateCache);
                      console.log('[AI /api/ai/route.ts] Non-batchable tool result:', toolResult);

                      if (toolResult.success) {
                        sendToolUpdate(toolCall, 'completed', toolResult.data);
                      } else {
                        sendToolUpdate(toolCall, 'failed', undefined, toolResult.error);
                        conversationMessages.push({
                          role: 'assistant',
                          content: `Tool call failed: ${toolResult.error}`
                        });
                        break;
                      }
                    }

                    // For non-batchable tools, add the tool result data directly to conversation history
                    // For batchable tools, just note that they were queued
                    if (isBatchableToolCall(toolCall.name)) {
                      // Batchable tools: just add a note that the operation was queued
                      conversationMessages.push({
                        role: 'assistant',
                        content: `Tool ${toolCall.name} queued for batch processing.`
                      });
                      console.log(`[AI Route] Added queued message for batchable tool ${toolCall.name}`);
                    } else {
                      // Non-batchable tools: add the actual result data as content the AI can understand
                      if (toolResult && toolResult.success) {
                        let resultMessage = `Tool ${toolCall.name} executed successfully.`;

                        // For read_board, provide the board data in a readable format
                        if (toolCall.name === 'read_board' && toolResult.data) {
                          const { elements, connections, elementCount, connectionCount } = toolResult.data;
                          resultMessage = `Board read successfully. Found ${elementCount} elements and ${connectionCount} connections.\n\nElements:\n`;

                          elements?.forEach((element: any) => {
                            resultMessage += `- ${element.type} (ID: ${element.id}): "${element.content}"${element.title ? ` [Title: ${element.title}]` : ''} at position (${element.position.x}, ${element.position.y})\n`;
                          });

                          if (connections?.length > 0) {
                            resultMessage += `\nConnections:\n`;
                            connections.forEach((conn: any) => {
                              resultMessage += `- Connection from ${conn.fromId} to ${conn.toId}${conn.label ? ` (${conn.label})` : ''}\n`;
                            });
                          }
                        } else {
                          // For other non-batchable tools, include the result data
                          resultMessage += ` Result: ${JSON.stringify(toolResult.data)}`;
                        }

                        conversationMessages.push({
                          role: 'assistant',
                          content: resultMessage
                        });
                        console.log(`[AI Route] Added result message for non-batchable tool ${toolCall.name}:`, resultMessage.substring(0, 200));
                      } else {
                        conversationMessages.push({
                          role: 'assistant',
                          content: `Tool ${toolCall.name} failed: ${toolResult?.error || 'Unknown error'}`
                        });
                        console.log(`[AI Route] Added error message for non-batchable tool ${toolCall.name}:`, toolResult?.error);
                        break;
                      }
                    }

                    // reset currentToolCall for next object
                    currentToolCall = null;

                    // For non-batchable tools, continue the loop to get AI's next response
                    // For batchable tools, also continue to allow more operations
                    break;
                  }
                }

                // Safety: cap buffer growth for malformed streams
                if (framingBuffer.length > 20000) {
                  console.warn('[AI_STREAM] Framing buffer too large, resetting');
                  framingBuffer = '';
                  lastSentLength = 0;
                  currentToolCall = null;
                }
              }
            }
          } finally {
            try {
              reader.releaseLock();
            } catch (error) {
              console.log('[AI_STREAM] Reader already released');
            }
          }
        }

        console.log(`[AI Route] Exited tool loop: hasMoreTools=${hasMoreTools}, toolCallCount=${toolCallCount}/${maxToolCalls}`);

        // Process batch operations before sending completion signal
        if (turnState.operations.length > 0 && boardId) {
          try {
            console.log(`[AI Route] End of turn detected, processing ${turnState.operations.length} batch operations for turn ${turnState.turnId}`);

            // Process batch operations directly (avoiding internal API call authentication issues)
            const batchResult = await processBatchOperationsDirectly(
              turnState.turnId,
              boardId,
              turnState.operations,
              request
            );

            // Send batch results to frontend
            console.log(`[AI Route] Sending batch results to frontend:`, {
              type: 'batch_complete',
              turn_id: turnState.turnId,
              results: batchResult.results
            });

            sendChunk({
              type: 'batch_complete',
              turn_id: turnState.turnId,
              results: batchResult.results
            });

            console.log(`[AI Route] Batch persist completed for turn ${turnState.turnId}`);

          } catch (error) {
            console.error(`[AI Route] Batch persist failed for turn ${turnState.turnId}:`, error);
            sendChunk({
              type: 'batch_error',
              turn_id: turnState.turnId,
              error: error instanceof Error ? error.message : 'Batch processing failed'
            });
          }
        }

        // Send completion signal (only if not already sent)
        if (!completionSent) {
          completionSent = true;
          console.log(`[AI Route] 🏁 Sending done message to complete stream`);
          sendChunk({ type: 'done' });
        }
        
      } catch (error) {
        console.error('Error in structured conversation:', error);
        sendChunk({
          type: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      } finally {
        streamClosed = true;
        try {
          controller.close();
        } catch (error: any) {
          if (error.code !== 'ERR_INVALID_STATE') {
            console.error('Error closing stream controller:', error);
          }
        }
      }
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    }
  });
}

export async function POST(request: NextRequest) {
  try {
    const { messages, model, response_format, stream, boardId, boardData } = await request.json();

    // CRITICAL DEBUG: Log what board data we receive from the client
    console.log(`[AI Route POST] Received request with boardId: ${boardId}`);
    console.log(`[AI Route POST] Board data received:`, boardData ? `${boardData.elements?.length || 0} elements, ${boardData.connections?.length || 0} connections` : 'null');
    if (boardData?.elements) {
      console.log(`[AI Route POST] Element IDs received:`, boardData.elements.map((e: any) => e.id));
    }

    if (!messages || !model) {
      return NextResponse.json({ error: 'Missing messages or model' }, { status: 400 });
    }

    // Convert messages to AIMessage format
    const aiMessages: AIMessage[] = messages.map((msg: any) => ({
      role: msg.role,
      content: msg.content
    }));

    // For non-streaming or JSON schema requests, use basic completion
    if (stream === false || response_format?.type === 'json_schema') {
      const payload: any = {
        model: model === 'standard' ? 'anthropic/claude-3.5-sonnet:beta' : model,
        messages: [
          { role: 'system', content: SYSTEM_PROMPT },
          ...aiMessages
        ],
        stream: true,
      };

      if (response_format && response_format.type === 'json_schema') {
        payload.response_format = response_format;
      }

      const apiKey = process.env.OPEN_ROUTER_API_KEY;
      if (!apiKey) {
        return NextResponse.json({ error: 'OpenRouter API key is not configured' }, { status: 500 });
      }

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': process.env.YOUR_SITE_URL || process.env.VERCEL_URL || 'http://localhost:3000',
          'X-Title': process.env.YOUR_SITE_NAME || 'Detective Board'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to parse OpenRouter error response' }));
        console.error('OpenRouter API error:', response.status, errorData);
        return NextResponse.json({ error: errorData.error || 'Failed to get response from AI service' }, { status: response.status });
      }

      return NextResponse.json(await response.json());
    }

    // Use new structured conversation processing for streaming
    return await processStructuredConversation(aiMessages, model, request, boardId, boardData);

  } catch (error: any) {
    console.error('[API /ai POST] Error:', error);
    let errorMessage = 'Failed to process AI request';
    let statusCode = 500;
    
    if (error instanceof SyntaxError) {
      errorMessage = 'Invalid request format';
      statusCode = 400;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    return NextResponse.json({ error: errorMessage }, { status: statusCode });
  }
} 