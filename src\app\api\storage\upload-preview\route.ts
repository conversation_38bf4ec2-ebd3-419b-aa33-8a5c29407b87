import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import type { Database } from '../../../../lib/database.types';

import { getAuthenticatedUser } from '@/utils/authUtils';
// Use the same bucket as regular images
const BUCKET_NAME = 'images';

// Helper to extract relative path (e.g., "uuid/filename.png") from various URL formats
function extractRelativePath(url: string | null): string | null {
  if (!url) return null;
  try {
    // Check if it's already a relative path (doesn't start with http)
    if (!url.startsWith('http')) {
      if (url.includes('/')) {
        return url;
      } else {
        console.warn(`Provided path is not a URL and doesn't seem relative: ${url}`);
        return null;
      }
    }

    // If it's a full URL, parse it
    const parsedUrl = new URL(url);
    const pathSegments = parsedUrl.pathname.split('/');

    // Find the bucket name ('images' in this case)
    const bucketName = 'images';
    const bucketIndex = pathSegments.indexOf(bucketName);

    if (bucketIndex !== -1 && bucketIndex < pathSegments.length - 1) {
      // Join the segments after the bucket name
      return pathSegments.slice(bucketIndex + 1).join('/');
    }

    console.warn(`Could not extract relative path from URL: ${url}`);
    return null;
  } catch (error) {
    console.error(`Error parsing URL for relative path extraction: ${url}`, error);
    return null;
  }
}

// Simple in-memory lock store (for this server instance only)
const uploadLocks = new Map<string, { timestamp: number; userId: string }>();
const LOCK_TIMEOUT = 30000; // 30 seconds

// Helper function to acquire a lock
function acquireLock(boardId: string, userId: string): boolean {
  const now = Date.now();
  const existingLock = uploadLocks.get(boardId);
  
  // If no lock exists or lock has expired, acquire it
  if (!existingLock || (now - existingLock.timestamp) > LOCK_TIMEOUT) {
    uploadLocks.set(boardId, { timestamp: now, userId });
    return true;
  }
  
  return false;
}

// Helper function to release a lock
function releaseLock(boardId: string): boolean {
  return uploadLocks.delete(boardId);
}

// Clean up expired locks periodically
setInterval(() => {
  const now = Date.now();
  for (const [boardId, lock] of uploadLocks.entries()) {
    if ((now - lock.timestamp) > LOCK_TIMEOUT) {
      uploadLocks.delete(boardId);
    }
  }
}, 10000); // Clean up every 10 seconds

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const { user, error: authError, supabase } = await getAuthenticatedUser();

    if (authError || !user) {
      console.error("Preview Upload Error:", authError);
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const boardId = formData.get('boardId') as string;

    if (!file) {
      return NextResponse.json({ message: 'No file provided' }, { status: 400 });
    }

    if (!boardId) {
      return NextResponse.json({ message: 'Board ID is required' }, { status: 400 });
    }

    // Check if the file is an image
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ message: 'Only image files are allowed' }, { status: 400 });
    }

    // Try to acquire lock
    const lockAcquired = acquireLock(boardId, user.id);

    if (!lockAcquired) {
      console.log('Preview upload already in progress for board:', boardId);
      return NextResponse.json({ message: 'Preview upload already in progress' }, { status: 409 });
    }

    try {
      // Verify user has access to the board (within the lock)
      const { data: board, error: boardError } = await supabase
        .from('boards')
        .select('id, user_id, preview_image_url')
        .eq('id', boardId)
        .single();

      if (boardError || !board) {
        return NextResponse.json({ message: 'Board not found' }, { status: 404 });
      }

      // Check if user is owner OR has shared access with edit rights
      if (board.user_id !== user.id) {
        const { data: sharedAccess, error: sharedError } = await supabase
          .from('board_sharing')
          .select('id, role')
          .eq('board_id', boardId)
          .eq('user_id', user.id)
          .maybeSingle();

        if (sharedError || !sharedAccess) {
          return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
        }
      }

      // Delete previous preview image if it exists
      if (board?.preview_image_url) {
        try {
          // Use the extractRelativePath helper to get the file path
          const previousPath = extractRelativePath(board.preview_image_url);

          if (previousPath) {
            console.log(`Deleting previous preview image: ${previousPath}`);

            // Delete the file from storage
            const { error: deleteError } = await supabase
              .storage
              .from(BUCKET_NAME)
              .remove([previousPath]);

            if (deleteError) {
              console.error(`CRITICAL: Failed to delete previous preview image (path: ${previousPath}): ${deleteError.message}. Aborting upload.`);
              return NextResponse.json({ message: 'Failed to remove previous preview image. Upload aborted.', error: deleteError.message }, { status: 500 });
            } else {
              console.log(`Successfully deleted previous preview image: ${previousPath}`);
            }
          } else {
            console.warn('Could not extract path from previous preview URL:', board.preview_image_url);
          }
        } catch (deleteError) {
          console.error('CRITICAL: An unexpected error occurred during deletion of previous preview. Aborting upload.', deleteError);
          return NextResponse.json({ message: 'An unexpected error occurred while removing previous preview.', error: deleteError instanceof Error ? deleteError.message : 'Unknown error' }, { status: 500 });
        }
      }

      // Generate a unique file path using the same structure as regular image uploads
      // This ensures previews are stored in user-specific folders
      const fileExt = 'jpg'; // Always use jpg for previews
      // Construct the path: user_id/preview-boardId-timestamp.jpg
      const filePath = `${user.id}/preview-${boardId}-${Date.now()}.${fileExt}`;

      // Convert the file to array buffer
      const arrayBuffer = await file.arrayBuffer();
      const fileBuffer = new Uint8Array(arrayBuffer);

      // Upload to Supabase Storage using the same bucket as regular images
      const { data, error } = await supabase
        .storage
        .from(BUCKET_NAME)
        .upload(filePath, fileBuffer, {
          contentType: file.type,
          cacheControl: '3600',
          upsert: true // Allow overwriting existing previews
        });

      if (error) {
        console.error('Preview upload error:', error);
        return NextResponse.json({ message: error.message }, { status: 500 });
      }

      if (!data) {
        return NextResponse.json({ message: 'Failed to upload preview' }, { status: 500 });
      }

      // Get the public URL for the image
      const { data: urlData } = await supabase
        .storage
        .from(BUCKET_NAME)
        .getPublicUrl(filePath);

      const publicUrl = urlData.publicUrl;

      // Update the board with the preview image URL
      const { error: updateError } = await supabase
        .from('boards')
        .update({ preview_image_url: publicUrl })
        .eq('id', boardId);

      if (updateError) {
        console.error('Error updating board with preview URL:', updateError);
        return NextResponse.json({ message: 'Preview uploaded but board update failed' }, { status: 500 });
      }

      return NextResponse.json({
        message: 'Preview uploaded successfully',
        path: publicUrl
      }, { status: 200 });

    } finally {
      // Always release the lock, regardless of success or failure
      releaseLock(boardId);
    }
    
  } catch (error) {
    console.error('Error handling preview upload:', error);
    return NextResponse.json({ 
      message: 'Server error processing upload',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 