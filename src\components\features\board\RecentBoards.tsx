'use client';

import React, { useState, useCallback, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft, PlusCircle, Trash, Calendar, Clock, FileText, Link as LinkIcon, Loader, Loader2, Edit, MoreHorizontal } from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import DeleteConfirmationModal from './DeleteConfirmationModal';
import ReadableFontWrapper from '@/components/layout/ReadableFontWrapper';
import SaveModal from '../detective/Modals/SaveModal';
import { useBoardsData, BoardItem } from '@/hooks/useBoardsData';
import { Skeleton } from '@/components/ui/skeleton';
import PreloadBoardImages from './PreloadBoardImages';

// Number of boards to load initially and for each "load more" click
const BOARDS_PER_PAGE = 6;

const RecentBoards: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const [loadingBoardId, setLoadingBoardId] = useState<string | null>(null);
  const [deletingBoardId, setDeletingBoardId] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [boardToDelete, setBoardToDelete] = useState<BoardItem | null>(null);
  
  // Pagination state
  const [limit] = useState(BOARDS_PER_PAGE);
  const [offset, setOffset] = useState(0);
  const [visibleUserBoards, setVisibleUserBoards] = useState<BoardItem[]>([]);
  const [visibleSharedBoards, setVisibleSharedBoards] = useState<BoardItem[]>([]);
  const [hasMoreUserBoards, setHasMoreUserBoards] = useState(true);
  const [hasMoreSharedBoards, setHasMoreSharedBoards] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Use the custom hook to fetch boards data
  const { 
    data: boardsData, 
    isLoading, 
    isError, 
    error, 
    refetch,
    isFetching
  } = useBoardsData(limit, offset);

  // Add new boards to the visible list when data is loaded
  React.useEffect(() => {
    if (boardsData) {
      if (offset === 0) {
        setVisibleUserBoards(boardsData.userBoards);
      } else {
        setVisibleUserBoards(prev => {
          const existingBoardIds = new Set(prev.map(board => board.id));
          const newBoards = boardsData.userBoards.filter(board => !existingBoardIds.has(board.id));
          return [...prev, ...newBoards];
        });
      }

      setVisibleSharedBoards(boardsData.sharedBoards);
      
      setHasMoreUserBoards(
        boardsData.pagination.totalUserBoards > (offset + boardsData.userBoards.length)
      );
      
      setHasMoreSharedBoards(
        boardsData.pagination.totalSharedBoards > boardsData.sharedBoards.length
      );
      
      setIsLoadingMore(false);
    }
  }, [boardsData, offset]);

  // Load more user boards
  const loadMoreUserBoards = useCallback(() => {
    if (hasMoreUserBoards && !isLoadingMore) {
      setIsLoadingMore(true);
      setOffset(prevOffset => prevOffset + BOARDS_PER_PAGE);
    }
  }, [hasMoreUserBoards, isLoadingMore]);
  
  // State for editing board name
  const [boardToEdit, setBoardToEdit] = useState<BoardItem | null>(null);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [newBoardNameInput, setNewBoardNameInput] = useState('');
  const [isUpdatingName, setIsUpdatingName] = useState(false);

  // Format date to a more readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Format relative time (e.g., "2 days ago")
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    } else {
      return formatDate(dateString);
    }
  };

  const handleBoardClick = (boardId: string) => {
    setLoadingBoardId(boardId);
    router.push(`/board/${boardId}`);
  };

  const openDeleteModal = (e: React.MouseEvent, board: BoardItem) => {
    e.stopPropagation();
    setBoardToDelete(board);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    // Allow animation to complete before resetting the board
    setTimeout(() => {
      setBoardToDelete(null);
    }, 200);
  };

  const handleDeleteBoard = async () => {
    if (!boardToDelete) return;
    
    const boardId = boardToDelete.id;
    setDeletingBoardId(boardId);
    
    try {
      const response = await fetch(`/api/board/delete?boardId=${boardId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete board');
      }
      
      // Update local state after deletion
      setVisibleUserBoards(prev => prev.filter(board => board.id !== boardId));
      
      // Reset offset and refetch to ensure counts are up to date
      setOffset(0);
      refetch();
      
      toast.success('Board deleted successfully');
    } catch (error) {
      console.error('Error deleting board:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete board');
    } finally {
      setDeletingBoardId(null);
      closeDeleteModal();
    }
  };

  const openEditModal = (e: React.MouseEvent, board: BoardItem) => {
    e.stopPropagation();
    setBoardToEdit(board);
    setNewBoardNameInput(board.boardName);
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setTimeout(() => {
      setBoardToEdit(null);
    }, 200); // Animation delay
  };

  const handleUpdateBoardName = async (updatedName: string) => {
    if (!boardToEdit || !updatedName.trim()) {
      toast.error('Board name cannot be empty.');
      return;
    }
    if (updatedName.trim() === boardToEdit.boardName) {
      closeEditModal();
      return;
    }

    setIsUpdatingName(true);
    try {
      const response = await fetch('/api/board/update-name', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          boardId: boardToEdit.id,
          newName: updatedName.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update board name');
      }

      // Update local state with new name
      setVisibleUserBoards(prev => 
        prev.map(board => 
          board.id === boardToEdit.id 
            ? { ...board, boardName: updatedName.trim() } 
            : board
        )
      );
      
      // Refetch boards to ensure everything is up to date
      refetch();
      
      toast.success('Board name updated successfully!');
      closeEditModal();
    } catch (error) {
      console.error('Error updating board name:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update board name');
    } finally {
      setIsUpdatingName(false);
    }
  };

  // Skeleton loaders for boards
  const BoardSkeleton = () => (
    <Card className="bg-noir-50 border-noir-100 hover:bg-noir-50/80 transition-colors duration-200">
      <CardContent className="p-6 relative">
        <Skeleton className="h-7 w-3/4 bg-noir-100 mb-3" />
        <Skeleton className="h-[140px] w-full bg-noir-900 mb-4" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-2/3 bg-noir-100" />
          <Skeleton className="h-4 w-1/2 bg-noir-100" />
        </div>
      </CardContent>
      <CardFooter className="px-6 py-4 border-t border-noir-100 flex justify-between">
        <div className="flex space-x-3">
          <Skeleton className="h-5 w-12 bg-noir-100" />
        </div>
        <div>
          <Skeleton className="h-8 w-16 bg-noir-100" />
        </div>
      </CardFooter>
    </Card>
  );

  // Component to render a board card
  const BoardCard = ({ board }: { board: BoardItem }) => {
    const [imageLoading, setImageLoading] = useState(true);
    const [imageError, setImageError] = useState(false);

    return (
      <Card 
        key={board.id} 
        className={`bg-noir-50 border-noir-100 hover:bg-noir-50/80 transition-colors duration-200 ${
          loadingBoardId === board.id || deletingBoardId === board.id ? 'opacity-70' : ''
        }`}
      >
        <CardContent className="p-6 relative">
          {loadingBoardId === board.id && (
            <div className="absolute inset-0 flex items-center justify-center bg-noir-100/30 z-10">
              <Loader className="animate-spin text-noir-accent" />
            </div>
          )}
          {deletingBoardId === board.id && (
            <div className="absolute inset-0 flex items-center justify-center bg-noir-100/30 z-10">
              <div className="flex flex-col items-center">
                <Loader className="animate-spin text-red-500" />
                <span className="mt-2 text-white">Deleting...</span>
              </div>
            </div>
          )}
          <div 
            className="cursor-pointer" 
            onClick={() => handleBoardClick(board.id)}
          >
            <h3 className="text-xl font-bold mb-3 hover:text-noir-accent transition-colors truncate">
              {board.boardName}
            </h3>
            
            {/* Board preview */}
            <div className="mb-4 aspect-video bg-noir-900 border border-noir-700 rounded-md overflow-hidden relative">
              {board.previewImageUrl ? (
                <>
                  {imageLoading && (
                    <div className="flex flex-col items-center justify-center w-full h-full bg-noir-900 p-6 absolute inset-0 z-10">
                      <Loader2 className="h-8 w-8 animate-spin text-noir-accent mb-2" />
                      <p className="text-white text-sm text-center">Loading preview...</p>
                    </div>
                  )}
                  {imageError && (
                    <div className="flex flex-col items-center justify-center w-full h-full bg-noir-900 p-6">
                      <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500 mb-2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="12"></line>
                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                      </svg>
                      <span className="text-sm text-gray-500">Preview unavailable</span>
                    </div>
                  )}
                  <img 
                    src={board.previewImageUrl} 
                    alt="Board preview" 
                    className={`w-full h-full object-cover ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
                    onLoad={() => setImageLoading(false)}
                    onError={() => {
                      setImageLoading(false);
                      setImageError(true);
                      if (process.env.NODE_ENV !== 'production') {
                        console.error('Error loading preview image:', board.previewImageUrl);
                      }
                    }}
                    loading="lazy"
                  />
                </>
              ) : (
                <div className="flex flex-col items-center justify-center w-full h-full bg-noir-900 p-6">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-500 mb-2">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <circle cx="8.5" cy="8.5" r="1.5"></circle>
                    <polyline points="21,15 16,10 5,21"></polyline>
                  </svg>
                  <span className="text-sm text-gray-500">No preview available</span>
                </div>
              )}
            </div>
          </div>
        <div className="flex items-center text-gray-400 mb-1 text-sm">
          <Clock size={14} className="mr-2" />
          <span>Updated {formatRelativeTime(board.updatedAt)}</span>
        </div>
        <div className="flex items-center text-gray-400 text-sm">
          <Calendar size={14} className="mr-2" />
          <span>Created {formatDate(board.createdAt)}</span>
        </div>
      </CardContent>
      <CardFooter className="px-6 py-4 border-t border-noir-100 flex justify-between">
        <div className="flex space-x-4 text-gray-400 text-sm">
          <div className="flex items-center">
            <FileText size={14} className="mr-1" />
            <span>{board.elementsCount}</span>
          </div>
          <div className="flex items-center">
            <LinkIcon size={14} className="mr-1" />
            <span>{board.connectionsCount}</span>
          </div>
        </div>
        <div>
          {board.isOwner && (
            <>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-gray-400 hover:text-blue-500 hover:bg-transparent mr-1"
                onClick={(e) => openEditModal(e, board)}
                disabled={deletingBoardId === board.id || loadingBoardId === board.id || isUpdatingName}
              >
                <Edit size={16} />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0 text-gray-400 hover:text-red-500 hover:bg-transparent"
                onClick={(e) => openDeleteModal(e, board)}
                disabled={deletingBoardId === board.id}
              >
                <Trash size={16} />
              </Button>
            </>
          )}
        </div>
      </CardFooter>
    </Card>
  );
  };

  return (
    <MainLayout>
      <ReadableFontWrapper>
        <div className="w-full min-h-screen px-4 py-12 text-white">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center mb-8">
              <Button 
                variant="ghost" 
                className="mr-4 text-white"
                onClick={() => router.push('/')}
              >
                <ArrowLeft className="mr-2" size={20} />
                Back to Home
              </Button>
              <h1 className="text-4xl font-bold">Recent Boards</h1>
            </div>

            {isLoading && offset === 0 ? (
              <>
                <div className="mb-6 flex items-center justify-between">
                  <h2 className="text-2xl font-bold">Your Boards</h2>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                  {Array(3).fill(0).map((_, i) => (
                    <BoardSkeleton key={`skeleton-${i}`} />
                  ))}
                </div>
              </>
            ) : isError ? (
              <Card className="bg-noir-50 border-noir-100 p-8 text-center">
                <CardContent className="flex flex-col items-center pt-6">
                  <p className="text-lg mb-6 text-red-400">{error instanceof Error ? error.message : 'An error occurred'}</p>
                  <Button 
                    className="bg-noir-accent hover:bg-noir-accent/90 text-white"
                    onClick={() => router.push('/')}
                  >
                    Back to Home
                  </Button>
                </CardContent>
              </Card>
            ) : visibleUserBoards.length === 0 && visibleSharedBoards.length === 0 ? (
              <Card className="bg-noir-50 border-noir-100 p-8 text-center">
                <CardContent className="flex flex-col items-center pt-6">
                  <p className="text-lg mb-6">You don't have any saved detective boards yet.</p>
                  <Button 
                    className="bg-noir-accent hover:bg-noir-accent/90 text-white"
                    onClick={() => router.push('/board/new')}
                  >
                    <PlusCircle className="mr-2" size={18} />
                    Create New Board
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <>
                <div className="mb-6 flex items-center justify-between">
                  <h2 className="text-2xl font-bold">Your Boards</h2>
                  <Button 
                    className="bg-noir-accent hover:bg-noir-accent/90 text-white"
                    onClick={() => router.push('/board/new')}
                  >
                    <PlusCircle className="mr-2" size={18} />
                    Create New Board
                  </Button>
                </div>
                
                {visibleUserBoards.length === 0 ? (
                  <p className="text-gray-400 mb-8">You haven't created any boards yet.</p>
                ) : (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                      {visibleUserBoards.map(board => (
                        <BoardCard key={board.id} board={board} />
                      ))}
                    </div>
                    
                    {/* Load More Button for User Boards */}
                    {hasMoreUserBoards && (
                      <div className="flex justify-center mb-12">
                        <Button
                          onClick={loadMoreUserBoards}
                          disabled={isLoadingMore}
                          className="bg-noir-accent hover:bg-noir-accent/90 text-white"
                        >
                          {isLoadingMore ? (
                            <>
                              <Loader size={20} className="animate-spin mr-2" />
                              <span>Loading...</span>
                            </>
                          ) : (
                            <span>Load More</span>
                          )}
                        </Button>
                      </div>
                    )}
                  </>
                )}

                {/* Shared boards section */}
                {visibleSharedBoards.length > 0 && (
                  <>
                    <div className="mb-6 flex items-center justify-between">
                      <h2 className="text-2xl font-bold">Boards Shared With Me</h2>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
                      {visibleSharedBoards.map(board => (
                        <BoardCard key={board.id} board={board} />
                      ))}
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </ReadableFontWrapper>

      {/* Delete Confirmation Modal */}
      {boardToDelete && (
        <DeleteConfirmationModal
          isOpen={deleteModalOpen}
          onClose={closeDeleteModal}
          onConfirm={handleDeleteBoard}
          boardName={boardToDelete.boardName}
          isDeleting={deletingBoardId === boardToDelete.id}
        />
      )}

      {/* Edit Board Name Modal (Now SaveModal) */}
      {boardToEdit && (
        <SaveModal
          isOpen={editModalOpen}
          onClose={closeEditModal}
          onSave={handleUpdateBoardName}
          initialBoardName={boardToEdit.boardName}
          isNewBoard={false}
        />
      )}

      {/* Preload next batch of board images */}
      {!isLoading && boardsData?.userBoards && (
        <PreloadBoardImages 
          boards={boardsData.userBoards.slice(visibleUserBoards.length)} 
          preloadCount={3}
        />
      )}
    </MainLayout>
  );
};

export default RecentBoards; 