'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Heart, MessageSquare, Loader2, Globe, Eye } from 'lucide-react';
import Image from 'next/image';
import { usePublicBoards, PublicBoard } from '@/hooks/usePublicBoards';
import { useSignedImageUrl } from '@/hooks/useSignedImageUrl';

// Board card component to handle image loading separately
const BoardCard = ({ 
  board, 
  index, 
  isLoading,
  onClick 
}: { 
  board: PublicBoard; 
  index: number;
  isLoading: boolean; 
  onClick: () => void;
}) => {
  // Use the signed URL hook to get properly signed URLs
  const { signedUrl, isLoading: imageLoading } = useSignedImageUrl(board.id, board.previewImageUrl);
  
  // Format date to a more readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else if (diffInDays < 30) {
      return `${Math.floor(diffInDays / 7)} weeks ago`;
    } else {
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };
  
  return (
    <div 
      className="group relative flex flex-col rounded-lg neo-blur overflow-hidden transition-all duration-300 hover:-translate-y-1 hover:bg-white/10 cursor-pointer animate-fade-up border border-noir-700 hover:border-noir-accent"
      onClick={onClick}
      style={{ animationDelay: `${0.1 * index}s` }}
    >
      {isLoading ? (
        <div className="flex flex-col items-center justify-center aspect-video bg-noir-900 p-10">
          <Loader2 className="h-12 w-12 animate-spin text-noir-accent mb-4" />
          <p className="text-white text-center">Loading board...</p>
        </div>
      ) : imageLoading && board.previewImageUrl ? (
        <div className="flex flex-col items-center justify-center aspect-video bg-noir-900 p-10">
          <Loader2 className="h-12 w-12 animate-spin text-noir-accent mb-4" />
          <p className="text-white text-center">Loading image...</p>
        </div>
      ) : (
        <>
          <div className="aspect-video bg-noir-900 overflow-hidden relative">
            {signedUrl ? (
              <Image 
                src={signedUrl} 
                alt={board.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                width={400}
                height={225}
                loading="lazy"
                placeholder="blur"
                blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEtAI8V7yQCgAAAABJRU5ErkJggg=="
                unoptimized={true}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-noir-800 to-noir-900">
                <Globe size={48} className="text-gray-600 opacity-50" />
              </div>
            )}
          </div>
          
          <div className="p-5">
            <h3 className="font-semibold text-white text-xl mb-3 truncate">{board.name}</h3>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4 text-gray-400 text-sm">
                <div className="flex items-center">
                  <div className="rounded-full bg-noir-800 p-1.5 mr-1 group-hover:bg-noir-accent transition-colors duration-300">
                    <Heart size={14} className="text-white" />
                  </div>
                  <span>{board.likes}</span>
                </div>
                
                {board.commentCount !== undefined && (
                  <div className="flex items-center">
                    <div className="rounded-full bg-noir-800 p-1.5 mr-1 group-hover:bg-blue-600 transition-colors duration-300">
                      <MessageSquare size={14} className="text-white" />
                    </div>
                    <span>{board.commentCount}</span>
                  </div>
                )}

                {board.totalViews !== undefined && (
                  <div className="flex items-center">
                    <div className="rounded-full bg-noir-800 p-1.5 mr-1 group-hover:bg-green-600 transition-colors duration-300">
                      <Eye size={14} className="text-white" />
                    </div>
                    <span>{board.totalViews}</span>
                  </div>
                )}
              </div>
              <div className="text-xs text-gray-400">
                {formatDate(board.publicSince)}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

interface PopularBoardsProps {
  limit?: number;
  searchQuery?: string;
  sortBy?: string;
  sortOrder?: string;
  showPopular?: boolean;
  timePeriod?: string;
  initialData?: {
    boards: PublicBoard[];
    pagination: {
      total: number;
      offset: number;
      limit: number;
      hasMore: boolean;
    };
  };
}

const PopularBoards: React.FC<PopularBoardsProps> = ({ 
  limit = 12,
  searchQuery = '',
  sortBy = 'likes',
  sortOrder = 'desc',
  showPopular = false,
  timePeriod = 'all',
  initialData
}) => {
  const router = useRouter();
  const [loadingBoardId, setLoadingBoardId] = useState<string | null>(null);

  // Use React Query hook for data fetching
  const { 
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    error
  } = usePublicBoards({
    limit,
    searchQuery,
    sortBy,
    sortOrder,
    timePeriod
  });

  // Flatten the pages of boards
  const popularBoards = data?.pages.flatMap(page => page.boards) || 
                        initialData?.boards || [];

  const handleBoardClick = (boardId: string) => {
    setLoadingBoardId(boardId);
    router.push(`/public-board/${boardId}`);
  };

  if (isLoading && popularBoards.length === 0) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-noir-accent" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-400">{(error as Error).message || 'Failed to load popular boards'}</p>
      </div>
    );
  }

  if (popularBoards.length === 0 && !isLoading) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-400">
          {searchQuery
            ? `No boards found matching "${searchQuery}"`
            : 'No public boards available at the moment.'}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {popularBoards.map((board, index) => (
          <BoardCard
            key={board.id}
            board={board}
            index={index}
            isLoading={loadingBoardId === board.id}
            onClick={() => handleBoardClick(board.id)}
          />
        ))}
      </div>

      {hasNextPage && (
        <div className="flex justify-center pt-6">
          <button
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
            className="px-6 py-2 bg-noir-800 hover:bg-noir-700 text-white rounded-md transition-colors disabled:opacity-50"
          >
            {isFetchingNextPage ? (
              <span className="flex items-center justify-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading more...
              </span>
            ) : (
              'Load More'
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default PopularBoards; 