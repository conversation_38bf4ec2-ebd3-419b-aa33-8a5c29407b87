import React, { useState, useEffect } from 'react';
import { motion } from '../../../../utils/MotionWrapper';
import { useDrag } from '../../../../context/DragContext';
import { X } from 'lucide-react';

interface ConnectionProps {
  id: string;
  from: { id: string; position: { x: number; y: number }; width?: number; height?: number };
  to: { id: string; position: { x: number; y: number }; width?: number; height?: number };
  onSelect: (id: string) => void;
  isSelected: boolean;
  onDelete?: (id: string) => void;
  presentationMode?: boolean;
  simplificationLevel?: number;
}

const Connection: React.FC<ConnectionProps> = ({
  id,
  from,
  to,
  onSelect,
  isSelected,
  onDelete,
  presentationMode = false,
  simplificationLevel = 0
}) => {

  const [isHovered, setIsHovered] = useState(false);
  const [pathData, setPathData] = useState('');
  
  // Use drag context for real-time position updates
  const { getItemPosition, isItemDragging } = useDrag();
  
  // Get real-time positions with fallback to the stored positions
  const fromPosition = getItemPosition(from.id, from.position);
  const toPosition = getItemPosition(to.id, to.position);
  
  // Calculate connection points using center of elements
  const fromW = from.width ?? 250; // Default width if not provided
  const fromH = from.height ?? 250; // Default height if not provided
  const toW = to.width ?? 250;     // Default width if not provided
  const toH = to.height ?? 250;     // Default height if not provided
  
  const startX = fromPosition.x + fromW / 2;
  const startY = fromPosition.y + 20; // Revert to 20px from the top
  
  const endX = toPosition.x + toW / 2;
  const endY = toPosition.y + 20;   // Revert to 20px from the top
  if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) {
      console.error(`[Connection ${id}] ERROR: NaN detected in calculated start/end points!`);
  }

  // Check if either connected item is being dragged
  const isDragging = isItemDragging(from.id) || isItemDragging(to.id);
  
  // Generate simple curved path with one intermediate point
  useEffect(() => {
    // Calculate the midpoint with a sag effect
    const midX = (startX + endX) / 2;
    
    // Calculate distance for proportional sag
    const dx = endX - startX;
    const dy = endY - startY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    // Scale sag based on distance, keeping it between 75px and 150px
    // For very short distances, use minimum 75px
    // For longer distances, scale up to 150px with a logistic growth curve
    const minSag = 75;
    const maxSag = 150;
    const sagScale = 0.2; // Controls how quickly sag increases with distance
    
    // Calculate sag with a formula that starts at minSag and approaches maxSag
    const sagAmount = minSag + (maxSag - minSag) * (1 - Math.exp(-sagScale * distance / 500));
    
    const midY = (startY + endY) / 2 + sagAmount;
    
    // Create a quadratic Bézier path
    const newPathData = `M ${startX} ${startY} Q ${midX} ${midY} ${endX} ${endY}`;
    
    if (isNaN(midX) || isNaN(midY)) {
      console.error(`[Connection ${id}] ERROR: NaN detected in calculated mid points!`);
    }
    if (typeof newPathData !== 'string' || newPathData.includes('NaN')) {
      console.error(`[Connection ${id}] ERROR: Invalid pathData generated: "${newPathData}"`);
    }

    setPathData(newPathData);
  }, [id, startX, startY, endX, endY]);

  // Determine stroke width based on state and simplification level
  let currentStrokeWidth = 3;
  if (simplificationLevel === 1) {
    currentStrokeWidth = isSelected ? 4 : isHovered ? 3 : 2;
  } else if (simplificationLevel === 2) {
    currentStrokeWidth = isSelected ? 3 : isHovered ? 2 : 2;
  } else { // simplificationLevel === 0 or undefined
    currentStrokeWidth = isSelected ? 5 : isHovered ? 4 : 3;
  }

  // Determine if pins and delete button should be shown
  const showPins = true; // Always show pins regardless of simplification level
  // Show delete button on hover OR when selected for better UX, and not in presentation mode
  const showDeleteButton = (isSelected || isHovered) && onDelete && !presentationMode;

  // Determine pin size and detail based on simplification level
  const pinHeadRadius = simplificationLevel === 0 ? 6 : simplificationLevel === 1 ? 5 : 4;
  const pinTopRadius = simplificationLevel === 0 ? 2 : simplificationLevel === 1 ? 1.5 : 1;
  const pinStrokeWidth = simplificationLevel === 0 ? 1 : simplificationLevel === 1 ? 0.75 : 0.5;

  // Determine filter based on simplification level
  const filterValue = simplificationLevel < 2 ? "url(#yarnWithShadow)" : "none";

  const handleClick = (e: React.MouseEvent) => {
    // If in presentation mode, don't allow selection
    if (presentationMode) return;
    
    e.preventDefault();
    e.stopPropagation();
    // Debug: trace connection select
    try { console.debug('[Connection] onSelect click', { id }); } catch {}
    onSelect(id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Debug: trace delete click
    try { console.debug('[Connection] onDelete click', { id }); } catch {}
    if (onDelete) {
      onDelete(id);
    }
  };

  try { // --- BEGIN TRY-CATCH ---
    return (
      <div 
        className="absolute top-0 left-0 w-full h-full"
        style={{ 
          overflow: 'visible', 
          zIndex: 10, // Higher z-index to appear above elements
          pointerEvents: 'none' // Allow pointer events to pass through this wrapper
        }}
      >
        <svg 
          width="100%" 
          height="100%" 
          className="absolute top-0 left-0"
          style={{ overflow: 'visible', pointerEvents: 'none' }}
        >
          {/* Define the yarn texture filter */}
          <defs>
            <filter id="yarnTexture">
              {/* Create some fractal noise */}
              <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="2" result="noise" />
              {/* Displace the stroke with the noise */}
              <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" />
            </filter>
            
            <filter id="yarnWithShadow">
              {/* First apply the yarn texture */}
              <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="2" result="noise" />
              <feDisplacementMap in="SourceGraphic" in2="noise" scale="2" result="textured" />
              
              {/* Then add a subtle drop shadow */}
              <feDropShadow dx="3" dy="4" stdDeviation="3.5" floodColor="rgba(0, 0, 0, 0.5)" />
            </filter>
          </defs>
          
          {/* Main connection path - yarn-like appearance */}
          <motion.path
            d={pathData}
            initial={{ 
              opacity: 0, 
              pathLength: 0,
              strokeWidth: 3 // Add explicit initial strokeWidth
            }}
            animate={{ 
              opacity: 1,
              pathLength: 1,
              stroke: isSelected ? '#ff0000' : isHovered ? '#ff6666' : '#C0392B',
              strokeWidth: currentStrokeWidth,
            }}
            transition={{ duration: isDragging ? 0 : 0.4 }} // Remove transition during dragging for instant updates
            className="cursor-pointer"
            onClick={handleClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            style={{
              pointerEvents: 'stroke',
              strokeLinecap: 'round',
              fill: 'none',
              filter: filterValue
            }}
          />
          
          {/* Invisible wider path for easier selection (no filter on this one) */}
          <path
            d={pathData}
            stroke="transparent"
            strokeWidth={20}
            style={{ pointerEvents: 'stroke', cursor: 'pointer', fill: 'none' }}
            onClick={handleClick}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          />
          
          {/* Delete button moved near end to ensure top-most drawing order */}
          {showDeleteButton && (
            <foreignObject
              x={(startX + endX) / 2 - 15}
              y={(startY + endY) / 2 - 15}
              width={30}
              height={30}
              style={{ pointerEvents: 'auto' }}
            >
              <div
                data-testid="connection-delete-button"
                className="w-full h-full flex items-center justify-center rounded-full bg-red-500 text-white cursor-pointer hover:bg-red-600 shadow-md"
                onClick={handleDelete}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                title="Delete connection"
                style={{ position: 'relative', zIndex: 2 }}
              >
                <X size={16} />
              </div>
            </foreignObject>
          )}
          
          {/* Start point pin (small red circle with shadow) */}
          {showPins && (
            <g pointerEvents="none">
              {/* Pin head */}
              <motion.circle
                cx={startX}
                cy={startY}
                r={pinHeadRadius}
                initial={{ 
                  opacity: 0,
                  strokeWidth: 1 // Add explicit initial strokeWidth
                }}
                animate={{ 
                  opacity: 1,
                  fill: '#B22222',
                  stroke: '#990000',
                  strokeWidth: pinStrokeWidth
                }}
                transition={{ duration: isDragging ? 0 : 0.2 }} // Remove transition during dragging
                style={{ filter: simplificationLevel < 2 ? 'drop-shadow(0px 2px 2px rgba(0,0,0,0.5))' : 'none' }}
              />
              
              {/* Pin top - only show for lower simplification levels */}
              {simplificationLevel < 2 && (
                <motion.circle
                  cx={startX}
                  cy={startY - 2}
                  r={pinTopRadius}
                  initial={{ 
                    opacity: 0,
                    strokeWidth: 0.5 // Add explicit initial strokeWidth
                  }}
                  animate={{ 
                    opacity: 1,
                    fill: '#ffcccc',
                    stroke: '#990000',
                    strokeWidth: pinStrokeWidth / 2
                  }}
                  transition={{ duration: isDragging ? 0 : 0.2 }} // Remove transition during dragging
                />
              )}
            </g>
          )}
          
          {/* End point pin (small red circle with shadow) */}
          {showPins && (
            <g pointerEvents="none">
              {/* Pin head */}
              <motion.circle
                cx={endX}
                cy={endY}
                r={pinHeadRadius}
                initial={{ 
                  opacity: 0,
                  strokeWidth: 1 // Add explicit initial strokeWidth
                }}
                animate={{ 
                  opacity: 1,
                  fill: '#B22222',
                  stroke: '#990000',
                  strokeWidth: pinStrokeWidth
                }}
                transition={{ duration: isDragging ? 0 : 0.2 }} // Remove transition during dragging
                style={{ filter: simplificationLevel < 2 ? 'drop-shadow(0px 2px 2px rgba(0,0,0,0.5))' : 'none' }}
              />
              {/* Pin top - only show for lower simplification levels */}
              {simplificationLevel < 2 && (
                <motion.circle
                  cx={endX}
                  cy={endY - 2}
                  r={pinTopRadius}
                  initial={{ 
                    opacity: 0,
                    strokeWidth: 0.5 // Add explicit initial strokeWidth
                  }}
                  animate={{ 
                    opacity: 1,
                    fill: '#ffcccc',
                    stroke: '#990000',
                    strokeWidth: pinStrokeWidth / 2
                  }}
                  transition={{ duration: isDragging ? 0 : 0.2 }} // Remove transition during dragging
                />
              )}
            </g>
          )}
        </svg>
      </div>
    );
  } catch (error) { // --- BEGIN CATCH BLOCK ---
    console.error(`[Connection ${id}] CRITICAL ERROR during SVG render:`, error);
    // Return null or a placeholder to prevent breaking the entire render tree
    return null;
  } // --- END CATCH BLOCK ---
};

export default Connection;